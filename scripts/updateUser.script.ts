import { app } from 'firebase-admin';
import { Test } from '@nestjs/testing';
import { FirebaseModule } from '../src/commons/modules/firebase/firebase.module';
import { FirebaseAuthError } from 'firebase-admin/auth';

async function updateUser() {
  const app = await Test.createTestingModule({
    imports: [
      FirebaseModule.forRoot({
        keyPath:
          '../src/keys/ideal-lumatera-firebase-adminsdk-fbsvc-e5e40d43f4.json',
      }),
    ],
  }).compile();
  const firebaseAdmin = app.get<app.App>('FIREBASE_ADMIN');

  try {
    const uid = 'LNcTTwmdbnRFCCWVIlM3O8OviaG3';
    await firebaseAdmin.auth().setCustomUserClaims(uid, {
      type: 'client',
    });
  } catch (e) {
    if (e instanceof FirebaseAuthError) {
      console.log(e.message);
    } else console.log(e);
  }
}

updateUser();
