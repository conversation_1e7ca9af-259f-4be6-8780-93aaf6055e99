# Repository Guidelines

## Project Structure & Module Organization
The NestJS application code resides in `src/`, with `main.ts` bootstrapping the HTTP server and `app.module.ts` wiring core providers. Feature code is grouped under `modules/` and `endpoints/`, while shared utilities live in `commons/`. Authentication keys and service-account stubs sit in `src/keys/`; keep replacements out of version control. Unit specs live beside their targets (for example `app.controller.spec.ts`), and end-to-end tests are in `test/`. Compiled output is emitted to `dist/` and should not be edited manually.

## Build, Test, and Development Commands
- `npm run start:dev` — start the API with live-reload for local development.
- `npm run build` — compile TypeScript to `dist/` for production deployment.
- `npm run start:prod` — serve the compiled bundle; use to validate production builds.
- `npm run lint` / `npm run format` — enforce ESLint plus Prettier formatting rules before committing.
- `npm test`, `npm run test:e2e`, `npm run test:cov` — run unit, e2e, and coverage suites.

## Coding Style & Naming Conventions
Use TypeScript with two-space indentation. Follow NestJS naming patterns: modules end with `.module.ts`, providers/services with `.service.ts`, controllers with `.controller.ts`, and schemas or validators with `.schema.ts` or `.zod.ts`. Keep imports path-alias friendly (`@/...`) and prefer dependency injection over direct instantiation. Run Prettier and ESLint (they are configured via `eslint.config.mjs`) before opening a PR.

## Testing Guidelines
Jest drives both unit and e2e suites. Name unit specs `*.spec.ts` in the same folder as the implementation and mirror the class/module under test. Add e2e specs in `test/` when touching request flows; use Supertest against the Nest testing module. Aim to keep coverage warnings clean by running `npm run test:cov` and addressing any uncovered branches before submission.

## Commit & Pull Request Guidelines
Follow Conventional Commits (`feat(auth): add firebase guard`) to match the existing history. Squash noisy WIP commits. Each PR should describe the change, note any config or env additions, and include local test results plus screenshots for HTTP changes when applicable. Link Jira or GitHub issues and request review once CI is green.

## Security & Configuration Tips
Load secrets through environment variables (`@nestjs/config`) and never commit real Firebase credentials; replace the sample in `src/keys/` locally only. Review `.env` additions with the team, and rotate credentials immediately if anything sensitive is exposed in logs or PRs.
