import {
  FirestoreDataConverter,
  QueryDocumentSnapshot,
} from 'firebase-admin/firestore';
import { ProjectModel, RawProjectModel } from './project.model.interface';
import { toDate as firestoreToDate } from '../../utils/firestore-date.util';

export class ProjectModelFirestore {
  static converter(): FirestoreDataConverter<
    ProjectModel,
    RawProjectModel<Date>
  > {
    return {
      toFirestore(project: ProjectModel): RawProjectModel<Date> {
        return {
          projectName: project.projectName,
          description: project.description,
          createdAt: project.createdAt,
          updatedAt: project.updatedAt,
          whatsappCredentials: project.whatsappCredentials,
          ownerUserUid: project.ownerUserUid,
        };
      },

      fromFirestore(snapshot: QueryDocumentSnapshot): ProjectModel {
        const data = snapshot.data() as RawProjectModel;
        return {
          uid: snapshot.id,
          projectName: data.projectName,
          description: data.description,
          createdAt: firestoreToDate(data.createdAt)!,
          updatedAt: firestoreToDate(data.updatedAt)!,
          whatsappCredentials: data.whatsappCredentials,
          ownerUserUid: data.ownerUserUid,
        };
      },
    };
  }
}
