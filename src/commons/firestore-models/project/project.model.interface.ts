import { firestore } from 'firebase-admin';

export interface WhatsappCredentials {
  provider: string;
  bearerToken: string;
  whatsappBusinessAccountId: string;
  phoneNumberId: string;
  phoneNumber: string;
  webhookVerificationToken: string;
}

export interface ProjectModel {
  uid: string;
  projectName: string;
  description: string;
  createdAt: Date;
  updatedAt: Date;
  whatsappCredentials: WhatsappCredentials;
  ownerUserUid: string;
}

export interface RawProjectModel<DATE = firestore.Timestamp> {
  projectName: string;
  description: string;
  createdAt: DATE;
  updatedAt: DATE;
  whatsappCredentials: WhatsappCredentials;
  ownerUserUid: string;
}
