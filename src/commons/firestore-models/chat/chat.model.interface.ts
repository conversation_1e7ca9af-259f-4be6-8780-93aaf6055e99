import { firestore } from 'firebase-admin';
import {
  MessageModel,
  RawMessageModel,
} from '../message/message.model.interface';

export interface ChatModel {
  id: string;
  phoneNumber: string;
  name: string;
  createdAt: Date;
  updatedAt: Date;
  lastMessage: MessageModel | null;
}

export interface RawChatModel<DATE = firestore.Timestamp> {
  phoneNumber: string;
  name: string;
  createdAt: DATE;
  updatedAt: DATE;
  lastMessage: RawMessageModel<DATE> | null;
}
