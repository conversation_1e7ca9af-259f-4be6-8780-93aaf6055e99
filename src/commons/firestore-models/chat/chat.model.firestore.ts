import {
  FirestoreDataConverter,
  QueryDocumentSnapshot,
} from 'firebase-admin/firestore';
import { ChatModel, RawChatModel } from './chat.model.interface';
import {
  MessageModel,
  RawMessageModel,
} from '../message/message.model.interface';
import { toDate as firestoreToDate } from '../../utils/firestore-date.util';

export class ChatModelFirestore {
  static converter(): FirestoreDataConverter<ChatModel, RawChatModel<Date>> {
    return {
      toFirestore(chat: ChatModel): RawChatModel<Date> {
        // Note: We don't include the id in the Firestore document as it's the document ID
        return {
          phoneNumber: chat.phoneNumber,
          name: chat.name,
          createdAt: chat.createdAt,
          updatedAt: chat.updatedAt,
          lastMessage: chat.lastMessage
            ? ChatModelFirestore.messageToFirestore(chat.lastMessage)
            : null,
        };
      },

      fromFirestore(snapshot: QueryDocumentSnapshot): ChatModel {
        const data = snapshot.data() as RawChatModel;
        return {
          id: snapshot.id,
          phoneNumber: data.phoneNumber,
          name: data.name,
          createdAt: firestoreToDate(data.createdAt)!,
          updatedAt: firestoreToDate(data.updatedAt)!,
          lastMessage: data.lastMessage
            ? ChatModelFirestore.messageFromFirestore(data.lastMessage)
            : null,
        };
      },
    };
  }

  private static messageToFirestore(
    message: MessageModel,
  ): RawMessageModel<Date> {
    return {
      message: {
        ...message.message,
        timestamp: message.message.timestamp,
        context: message.message.context ?? null,
      },
      statuses: message.statuses
        ? {
            latest: message.statuses.latest,
            timestamp: message.statuses.timestamp,
            details: {
              sent: message.statuses.details.sent,
              delivered: message.statuses.details.delivered,
              read: message.statuses.details.read,
              failed: message.statuses.details.failed,
            },
          }
        : null,
      direction: message.direction,
      createdAt: message.createdAt,
    };
  }

  private static messageFromFirestore(raw: RawMessageModel): MessageModel {
    const statuses = raw.statuses
      ? {
          latest: raw.statuses.latest,
          timestamp: firestoreToDate(raw.statuses.timestamp)!,
          details: {
            sent: firestoreToDate(raw.statuses.details.sent)!,
            delivered: firestoreToDate(raw.statuses.details.delivered)!,
            read: firestoreToDate(raw.statuses.details.read)!,
            failed: firestoreToDate(raw.statuses.details.failed)!,
          },
        }
      : null;

    return {
      message: {
        ...raw.message,
        timestamp: firestoreToDate(raw.message.timestamp)!,
        context: raw.message.context ?? null,
      },
      statuses,
      direction: raw.direction,
      createdAt: firestoreToDate(raw.createdAt)!,
    };
  }
}
