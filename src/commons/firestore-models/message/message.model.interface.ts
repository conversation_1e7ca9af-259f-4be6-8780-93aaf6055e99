import { firestore } from 'firebase-admin';

export type TMessageType =
  | 'text'
  | 'image'
  | 'audio'
  | 'video'
  | 'document'
  | 'sticker'
  | 'location'
  | 'contacts'
  | 'reaction'
  | 'interactive'
  | 'button'
  | 'order'
  | 'system'
  | 'unsupported';

export interface MessageContext {
  forwarded: boolean;
  frequently_forwarded: boolean;
  from: string | null;
  id: string | null;
  referred_product?: object;
}

export interface TextMessage {
  text?: {
    body: string;
  };
}

export interface ReactionMessage {
  reaction?: {
    message_id: string;
    emoji: string;
  };
}

export interface MediaMessageDetail {
  id: string;
  mime_type: string;
  sha256: string;
  caption?: string;
}

export interface MediaMessage {
  image?: MediaMessageDetail;
  audio?: MediaMessageDetail;
  video?: MediaMessageDetail;
  document?: MediaMessageDetail;
  sticker?: MediaMessageDetail;
}

export interface LocationMessage {
  location?: {
    latitude: string;
    longitude: string;
    name?: string;
    address?: string;
  };
}

export interface ContactsMessage {
  contacts?: {
    emails: {
      email: string;
      type: string;
    }[];
    name: {
      formatted_name: string;
      first_name: string;
      last_name: string;
      middle_name: string;
      suffix: string;
      prefix: string;
    };
    phones: {
      phone: string;
      type: string;
      wa_id: string;
    }[];
    urls: {
      url: string;
      type: string;
    }[];
  }[];
}

export interface QuickReplyButtonMessage {
  button?: {
    payload: string;
    text: string;
  };
}

export interface InteractiveListMessageAndQuickReplyButtonMessage {
  interactive?: {
    type: 'list_reply' | 'button_reply';
    list_reply?: {
      id: string;
      title: string;
      description?: string;
    };
    button_reply?: {
      id: string;
      title: string;
    };
  };
}

export interface WhatsappAdMessage {
  referral?: {
    source_type: string;
    source_id?: string;
    source_url?: string;
    body?: string;
    headline?: string;
    media_type?: string;
    image_url?: string;
    video_url?: string;
    thumbnail_url?: string;
    ctwa_clid?: string;
  };
}

export interface Statuses<DATE = firestore.Timestamp> {
  latest: 'sent' | 'delivered' | 'read' | 'failed';
  timestamp: DATE;
  details: {
    sent: DATE;
    delivered: DATE;
    read: DATE;
    failed: DATE;
  };
}

export interface MessageType
  extends TextMessage,
    ReactionMessage,
    MediaMessage,
    LocationMessage,
    ContactsMessage,
    QuickReplyButtonMessage,
    InteractiveListMessageAndQuickReplyButtonMessage,
    WhatsappAdMessage {}

export interface Message<DATE = firestore.Timestamp> extends MessageType {
  id: string;
  from: string;
  timestamp: DATE;
  type: TMessageType;
  context?: MessageContext | null;
}

export interface MessageModel {
  message: Message<Date>;
  statuses: Statuses<Date> | null;
  direction: 'in' | 'out';
  createdAt: Date;
  sentBy?: {
    uid: string;
    name: string;
    email: string;
  };
}

export interface RawMessageModel<DATE = firestore.Timestamp> {
  message: Message<DATE>;
  statuses: Statuses<DATE> | null;
  direction: 'in' | 'out';
  createdAt: DATE;
  sentBy?: {
    uid: string;
    name: string;
    email: string;
  };
}
