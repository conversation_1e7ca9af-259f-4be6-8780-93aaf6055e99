import {
  FirestoreDataConverter,
  QueryDocumentSnapshot,
} from 'firebase-admin/firestore';
import { MessageModel, RawMessageModel } from './message.model.interface';
import { toDate as firestoreToDate } from '../../utils/firestore-date.util';

export class MessageModelFirestore {
  static converter(): FirestoreDataConverter<
    MessageModel,
    RawMessageModel<Date>
  > {
    return {
      toFirestore(message: MessageModel): RawMessageModel<Date> {
        return {
          message: {
            ...message.message,
            timestamp: message.message.timestamp,
          },
          statuses: message.statuses,
          direction: message.direction,
          createdAt: message.createdAt,
        };
      },

      fromFirestore(snapshot: QueryDocumentSnapshot): MessageModel {
        const data = snapshot.data() as RawMessageModel;
        return {
          message: {
            ...data.message,
            timestamp: firestoreToDate(data.message.timestamp)!,
            context: data.message.context
              ? {
                  ...data.message.context,
                  from: data.message.context.from,
                  id: data.message.context.id,
                }
              : null,
          },
          statuses: data.statuses
            ? {
                ...data.statuses,
                timestamp: firestoreToDate(data.statuses.timestamp)!,
                details: {
                  sent: firestoreToDate(data.statuses.details.sent)!,
                  delivered: firestoreToDate(data.statuses.details.delivered)!,
                  read: firestoreToDate(data.statuses.details.read)!,
                  failed: firestoreToDate(data.statuses.details.failed)!,
                },
              }
            : null,
          direction: data.direction,
          createdAt: firestoreToDate(data.createdAt)!,
        };
      },
    };
  }
}
