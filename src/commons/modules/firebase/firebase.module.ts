import { DynamicModule, Module, Provider } from '@nestjs/common';
import firebaseAdmin from 'firebase-admin';
import { getApps } from 'firebase-admin/app';

@Module({})
export class FirebaseModule {
  public static forRoot(options?: { keyPath: string }): DynamicModule {
    const keyPath =
      options?.keyPath ||
      './src/keys/ideal-lumatera-firebase-adminsdk-fbsvc-e5e40d43f4.json';

    const providers: Provider[] = [
      {
        provide: 'FIREBASE_ADMIN',
        useFactory: () => {
          if (getApps().length > 0) {
            return firebaseAdmin.app();
          }
          return firebaseAdmin.initializeApp({
            credential: firebaseAdmin.credential.cert(keyPath),
          });
        },
      },
    ];
    return {
      global: true,
      module: FirebaseModule,
      providers: providers,
      exports: providers,
    };
  }
}
