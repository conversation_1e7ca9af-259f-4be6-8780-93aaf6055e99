import { Injectable, InternalServerErrorException } from '@nestjs/common';
import {
  MetaWhatsappDeleteMediaResponse,
  MetaWhatsappGetMediaResponse,
  MetaWhatsappSendMessageResponse,
  MetaWhatsappUploadMediaResponse,
  SendMessageParams,
  UploadMediaParams,
} from './meta-whatsapp.interface';
import { HttpService } from '@nestjs/axios';
import { firstValueFrom } from 'rxjs';
import FormData from 'form-data';

@Injectable()
export class MetaWhatsappService {
  constructor(private readonly httpService: HttpService) {}

  public async sendMessage({
    to,
    message,
    accessToken,
    phoneNumberId,
    messagingProduct = 'whatsapp',
    recipientType,
    context,
  }: SendMessageParams): Promise<MetaWhatsappSendMessageResponse> {
    if (!accessToken) {
      throw new InternalServerErrorException(
        'Meta WhatsApp access token is not configured',
      );
    }

    if (!phoneNumberId) {
      throw new InternalServerErrorException(
        'Meta WhatsApp phone number id is not configured',
      );
    }

    const endpoint = `/${phoneNumberId}/messages`;
    const body = {
      messaging_product: messagingProduct,
      to,
      ...(recipientType ? { recipient_type: recipientType } : {}),
      ...(context ? { context } : {}),
      ...message,
    };

    const send = this.httpService.post<MetaWhatsappSendMessageResponse>(
      endpoint,
      body,
      {
        headers: {
          Authorization: `Bearer ${accessToken}`,
        },
      },
    );

    try {
      const fetch = await firstValueFrom(send);
      return fetch.data;
    } catch (error) {
      throw new InternalServerErrorException(
        'Failed to send WhatsApp message',
        {
          cause: error instanceof Error ? error : undefined,
          description: 'Meta WhatsApp sendMessage call failed',
        },
      );
    }
  }

  public async uploadMedia({
    file,
    filename,
    mimeType,
    messagingProduct = 'whatsapp',
    caption,
    accessToken,
    phoneNumberId,
  }: UploadMediaParams): Promise<MetaWhatsappUploadMediaResponse> {
    if (!accessToken) {
      throw new InternalServerErrorException(
        'Meta WhatsApp access token is not configured',
      );
    }

    if (!phoneNumberId) {
      throw new InternalServerErrorException(
        'Meta WhatsApp phone number id is not configured',
      );
    }

    if (!mimeType) {
      throw new InternalServerErrorException(
        'Meta WhatsApp media mime type is not configured',
      );
    }

    const endpoint = `/${phoneNumberId}/media`;
    const formData = new FormData();

    formData.append('messaging_product', messagingProduct);
    formData.append('type', mimeType);
    if (caption) {
      formData.append('caption', caption);
    }

    formData.append('file', file, {
      filename: filename ?? 'media',
      contentType: mimeType,
    });

    const uploadRequest =
      this.httpService.post<MetaWhatsappUploadMediaResponse>(
        endpoint,
        formData,
        {
          headers: {
            ...formData.getHeaders(),
            Authorization: `Bearer ${accessToken}`,
          },
        },
      );

    try {
      const response = await firstValueFrom(uploadRequest);
      return response.data;
    } catch (error) {
      throw new InternalServerErrorException(
        'Failed to upload WhatsApp media',
        {
          cause: error instanceof Error ? error : undefined,
          description: 'Meta WhatsApp uploadMedia call failed',
        },
      );
    }
  }

  public async getMedia(
    mediaId: string,
    accessToken: string,
  ): Promise<MetaWhatsappGetMediaResponse> {
    if (!accessToken) {
      throw new InternalServerErrorException(
        'Meta WhatsApp access token is not configured',
      );
    }

    if (!mediaId) {
      throw new InternalServerErrorException(
        'Meta WhatsApp media id is not provided',
      );
    }

    const endpoint = `/${mediaId}`;

    const request = this.httpService.get<MetaWhatsappGetMediaResponse>(
      endpoint,
      {
        headers: {
          Authorization: `Bearer ${accessToken}`,
        },
      },
    );

    try {
      const response = await firstValueFrom(request);
      return response.data;
    } catch (error) {
      throw new InternalServerErrorException('Failed to get WhatsApp media', {
        cause: error instanceof Error ? error : undefined,
        description: 'Meta WhatsApp getMedia call failed',
      });
    }
  }

  public async deleteMedia(
    mediaId: string,
    accessToken: string,
  ): Promise<MetaWhatsappDeleteMediaResponse> {
    if (!accessToken) {
      throw new InternalServerErrorException(
        'Meta WhatsApp access token is not configured',
      );
    }

    if (!mediaId) {
      throw new InternalServerErrorException(
        'Meta WhatsApp media id is not provided',
      );
    }

    const endpoint = `/${mediaId}`;

    const request = this.httpService.delete<MetaWhatsappDeleteMediaResponse>(
      endpoint,
      {
        headers: {
          Authorization: `Bearer ${accessToken}`,
        },
      },
    );

    try {
      const response = await firstValueFrom(request);
      return response.data;
    } catch (error) {
      throw this.handleMetaError(error, 'deleteMedia');
    }
  }
}
