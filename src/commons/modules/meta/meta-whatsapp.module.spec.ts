import { MetaWhatsappService } from './meta-whatsapp.service';
import { SendMessageParams } from './meta-whatsapp.interface';
import { Test } from '@nestjs/testing';
import { FirebaseModule } from '../firebase/firebase.module';
import { ConfigModule } from '@nestjs/config';
import { app } from 'firebase-admin';
import { ProjectModelFirestore } from '../../firestore-models/project/project.model.firestore';
import { MetaWhatsappModule } from './meta-whatsapp.module';
import { HttpService } from '@nestjs/axios';

describe('MetaWhatsappModule', () => {
  let metaWhatsappService: MetaWhatsappService;
  let firebaseAdmin: app.App;
  let httpService: HttpService;

  let projectId: string;
  let phoneNumberId: string;
  let accessToken: string;
  let phoneNumberTarget: string;

  beforeEach(async () => {
    const module = await Test.createTestingModule({
      imports: [
        await ConfigModule.forRoot(),
        FirebaseModule.forRoot(),
        MetaWhatsappModule,
      ],
    }).compile();

    metaWhatsappService = module.get<MetaWhatsappService>(MetaWhatsappService);
    httpService = module.get<HttpService>(HttpService);
    firebaseAdmin = module.get<app.App>('FIREBASE_ADMIN');
    projectId = process.env.TEST_PROJECT_ID || '123';
    phoneNumberTarget = process.env.TEST_PHONE_NUMBER_TARGET || '123';

    const docProject = firebaseAdmin
      .firestore()
      .collection('projects')
      .doc(projectId);

    const getProject = await docProject
      .withConverter(ProjectModelFirestore.converter())
      .get();

    const project = getProject.data()!;
    phoneNumberId = project.whatsappCredentials.phoneNumberId;
    accessToken = project.whatsappCredentials.bearerToken;
  });

  it('should send text message successfully', async () => {
    const payload: SendMessageParams = {
      to: phoneNumberTarget,
      message: {
        type: 'text',
        text: {
          body: 'Hello World',
          preview_url: false,
        },
      },
      accessToken: accessToken,
      phoneNumberId: phoneNumberId,
    };

    const send = await metaWhatsappService.sendMessage(payload);
    expect(send).toBeDefined();
    expect(send.messages).toBeDefined();
    expect(send.messages?.length).toBeGreaterThan(0);
    expect(send.messages![0].id).toBeDefined();
  });

  it('should upload media successfully', async () => {
    // TODO: Add test for upload media
  });
});
