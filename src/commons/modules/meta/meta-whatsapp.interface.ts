export type MetaWhatsappErrorPayload = {
  message?: string;
  type?: string;
  code?: number;
  error_subcode?: number;
  error_user_msg?: string;
  error_user_title?: string;
  fbtrace_id?: string;
};

export type MetaWhatsappSendMessageResponse = {
  messages?: Array<{ id: string }>;
  contacts?: Array<{ input: string; wa_id: string }>;
  messaging_product?: 'whatsapp';
  error?: MetaWhatsappErrorPayload;
};

export type MetaWhatsappUploadMediaResponse = {
  id: string;
  error?: MetaWhatsappErrorPayload;
};

export type MetaWhatsappMessagingProduct = 'whatsapp';

export type MetaWhatsappTextMessage = {
  type: 'text';
  text: {
    body: string;
    preview_url?: boolean;
  };
};

export type MetaWhatsappTemplateMessage = {
  type: 'template';
  template: {
    name: string;
    language: {
      code: string;
    };
    components?: Array<Record<string, unknown>>;
  };
};

type MetaWhatsappMediaCommon = {
  id?: string;
  link?: string;
  caption?: string;
};

export type MetaWhatsappImageMessage = {
  type: 'image';
  image: MetaWhatsappMediaCommon;
};

export type MetaWhatsappVideoMessage = {
  type: 'video';
  video: MetaWhatsappMediaCommon;
};

export type MetaWhatsappAudioMessage = {
  type: 'audio';
  audio: Omit<MetaWhatsappMediaCommon, 'caption'>;
};

export type MetaWhatsappDocumentMessage = {
  type: 'document';
  document: MetaWhatsappMediaCommon & {
    filename?: string;
  };
};

export type MetaWhatsappStickerMessage = {
  type: 'sticker';
  sticker: {
    id?: string;
    link?: string;
  };
};

export type MetaWhatsappLocationMessage = {
  type: 'location';
  location: {
    latitude: string;
    longitude: string;
    name?: string;
    address?: string;
  };
};

export type MetaWhatsappContactsMessage = {
  type: 'contacts';
  contacts: Array<Record<string, unknown>>;
};

export type MetaWhatsappInteractiveMessage = {
  type: 'interactive';
  interactive: {
    type: string;
    header?: Record<string, unknown>;
    body: Record<string, unknown>;
    footer?: Record<string, unknown>;
    action: Record<string, unknown>;
  };
};

export type MetaWhatsappReactionMessage = {
  type: 'reaction';
  reaction: {
    message_id: string;
    emoji: string;
  };
};

export type MetaWhatsappMessage =
  | MetaWhatsappTextMessage
  | MetaWhatsappTemplateMessage
  | MetaWhatsappImageMessage
  | MetaWhatsappVideoMessage
  | MetaWhatsappAudioMessage
  | MetaWhatsappDocumentMessage
  | MetaWhatsappStickerMessage
  | MetaWhatsappLocationMessage
  | MetaWhatsappContactsMessage
  | MetaWhatsappInteractiveMessage
  | MetaWhatsappReactionMessage;

export type SendMessageParams<
  TMessage extends MetaWhatsappMessage = MetaWhatsappMessage,
> = {
  to: string;
  message: TMessage;
  accessToken: string;
  phoneNumberId: string;
  messagingProduct?: MetaWhatsappMessagingProduct;
  recipientType?: string;
  context?: {
    message_id: string;
  };
};

export type SendTextMessageParams = Omit<
  SendMessageParams<MetaWhatsappTextMessage>,
  'message'
> & {
  body: string;
  previewUrl?: boolean;
};

export type UploadMediaParams = {
  file: Buffer | NodeJS.ReadableStream;
  filename?: string;
  mimeType: string;
  messagingProduct?: MetaWhatsappMessagingProduct;
  caption?: string;
  accessToken: string;
  phoneNumberId: string;
};

export type MetaWhatsappGetMediaResponse = {
  messaging_product: 'whatsapp';
  url: string;
  mime_type: string;
  sha256: string;
  file_size: string;
  id: string;
  error?: MetaWhatsappErrorPayload;
};

export type MetaWhatsappDeleteMediaResponse = {
  success: boolean;
  error?: MetaWhatsappErrorPayload;
};
