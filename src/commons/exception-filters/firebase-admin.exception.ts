import {
  ArgumentsHost,
  Catch,
  ExceptionFilter,
  HttpStatus,
} from '@nestjs/common';

import { Response } from 'express';
import { FirebaseAuthError } from 'firebase-admin/auth';

@Catch(FirebaseAuthError)
export class FirebaseAdminException implements ExceptionFilter {
  catch(exception: FirebaseAuthError, host: ArgumentsHost) {
    const ctx = host.switchToHttp();
    const response = ctx.getResponse<Response>();

    return response.status(HttpStatus.INTERNAL_SERVER_ERROR).json({
      message: exception.message,
    });
  }
}
