import { Timestamp } from 'firebase-admin/firestore';

type FirestoreDateValue = Timestamp | Date | null | undefined;

type HasToDateMethod = {
  toDate: () => Date;
};

export const toDate = (value: FirestoreDateValue): Date | null => {
  if (value == null) {
    return null;
  }

  if (value instanceof Date) {
    return value;
  }

  if (value instanceof Timestamp) {
    return value.toDate();
  }

  if (typeof (value as HasToDateMethod).toDate === 'function') {
    return (value as HasToDateMethod).toDate();
  }

  return null;
};
