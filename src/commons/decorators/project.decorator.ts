import { createParamDecorator, ExecutionContext } from '@nestjs/common';
import { Request } from 'express';
import type { ProjectModel } from '../firestore-models/project/project.model.interface';

export const CurrentProject = createParamDecorator(
  (data: unknown, ctx: ExecutionContext): ProjectModel => {
    const request = ctx
      .switchToHttp()
      .getRequest<Request & { project: ProjectModel }>();

    return request.project;
  },
);
