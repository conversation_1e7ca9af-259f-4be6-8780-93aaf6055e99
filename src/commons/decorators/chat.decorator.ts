import {
  createParamDecorator,
  ExecutionContext,
  InternalServerErrorException,
} from '@nestjs/common';
import { Request } from 'express';
import type { ChatModel } from '../firestore-models/chat/chat.model.interface';

export const CurrentChat = createParamDecorator(
  (data: unknown, ctx: ExecutionContext): ChatModel => {
    const request = ctx
      .switchToHttp()
      .getRequest<Request & { chat?: ChatModel }>();

    if (!request.chat) {
      throw new InternalServerErrorException(
        'Chat not found in request. Ensure ChatGuard is applied.',
      );
    }

    return request.chat;
  },
);
