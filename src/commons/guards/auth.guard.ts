import {
  CanActivate,
  ExecutionContext,
  Inject,
  Injectable,
} from '@nestjs/common';
import { app } from 'firebase-admin';
import { Request } from 'express';

@Injectable()
export class AuthGuard implements CanActivate {
  constructor(
    @Inject('FIREBASE_ADMIN') private readonly firebaseAdmin: app.App,
  ) {}

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const request = context
      .switchToHttp()
      .getRequest<Request & { user: any }>();
    const token = request.headers.authorization?.split(' ')[1];

    if (!token) {
      return false;
    }

    try {
      const decodedUser = await this.firebaseAdmin.auth().verifyIdToken(token);
      const user = await this.firebaseAdmin.auth().getUser(decodedUser.uid);
      request.user = Object.freeze(user);
      return true;
    } catch {
      return false;
    }
  }
}
