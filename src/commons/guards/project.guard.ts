import {
  BadRequestException,
  CanActivate,
  ExecutionContext,
  Inject,
  Injectable,
  NotFoundException,
  UnauthorizedException,
} from '@nestjs/common';
import { Request } from 'express';
import { app, auth, firestore } from 'firebase-admin';
import type { ProjectModel } from '../firestore-models/project/project.model.interface';
import { ProjectModelFirestore } from '../firestore-models/project/project.model.firestore';

@Injectable()
export class ProjectGuard implements CanActivate {
  private readonly projectCollection: firestore.CollectionReference<ProjectModel>;

  constructor(
    @Inject('FIREBASE_ADMIN') private readonly firebaseAdmin: app.App,
  ) {
    this.projectCollection = this.firebaseAdmin
      .firestore()
      .collection('projects')
      .withConverter(ProjectModelFirestore.converter());
  }

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const request = context.switchToHttp().getRequest<
      Request & {
        user?: auth.UserRecord;
        project?: ProjectModel;
      }
    >();

    if (!request.user) {
      throw new UnauthorizedException();
    }

    const projectId = request.params.projectId;

    if (!projectId) {
      throw new BadRequestException('Project ID is required');
    }

    const projectSnapshot = await this.projectCollection
      .where('ownerUserUid', '==', request.user.uid)
      .where(firestore.FieldPath.documentId(), '==', projectId)
      .limit(1)
      .get();

    if (projectSnapshot.empty) {
      throw new NotFoundException('Project not found');
    }

    request.project = Object.freeze(projectSnapshot.docs[0].data());

    return true;
  }
}
