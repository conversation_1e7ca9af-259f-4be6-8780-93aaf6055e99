import {
  BadRequestException,
  CanActivate,
  ExecutionContext,
  Inject,
  Injectable,
  Logger,
  NotFoundException,
  UnauthorizedException,
} from '@nestjs/common';
import { Request } from 'express';
import { app, auth, firestore } from 'firebase-admin';
import type { ChatModel } from '../firestore-models/chat/chat.model.interface';
import { ChatModelFirestore } from '../firestore-models/chat/chat.model.firestore';
import type { ProjectModel } from '../firestore-models/project/project.model.interface';

@Injectable()
export class ChatGuard implements CanActivate {
  private readonly logger = new Logger(ChatGuard.name);
  private readonly chatCollection: firestore.CollectionReference<ChatModel>;

  constructor(
    @Inject('FIREBASE_ADMIN') private readonly firebaseAdmin: app.App,
  ) {
    this.chatCollection = this.firebaseAdmin
      .firestore()
      .collection('chats')
      .withConverter(ChatModelFirestore.converter());
  }

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const request = context.switchToHttp().getRequest<
      Request & {
        user?: auth.UserRecord;
        project?: ProjectModel;
        chat?: ChatModel;
      }
    >();

    // Check if user is authenticated (should be handled by AuthGuard)
    if (!request.user) {
      throw new UnauthorizedException();
    }

    // Check if project is available (should be handled by ProjectGuard)
    if (!request.project) {
      throw new UnauthorizedException();
    }

    const projectId = request.params.projectId;
    const chatId = request.params.chatId;

    // Validate that we have the required parameters
    if (!projectId) {
      throw new BadRequestException('Project ID is required');
    }

    if (!chatId) {
      throw new BadRequestException('Chat ID is required');
    }

    try {
      // Query the chat within the project subcollection
      const chatSnapshot = await this.firebaseAdmin
        .firestore()
        .collection('projects')
        .doc(projectId)
        .collection('chats')
        .doc(chatId)
        .withConverter(ChatModelFirestore.converter())
        .get();

      // Check if chat exists
      if (!chatSnapshot.exists) {
        this.logger.warn(`Chat ${chatId} not found in project ${projectId}`);
        throw new NotFoundException('Chat not found');
      }

      // Attach the chat data to the request object
      const chatData = chatSnapshot.data();
      if (!chatData) {
        this.logger.error(
          `Chat ${chatId} exists but has no data in project ${projectId}`,
        );
        throw new NotFoundException('Chat data is invalid');
      }

      request.chat = Object.freeze(chatData);

      return true;
    } catch (error: any) {
      // Handle Firestore errors specifically
      if (error instanceof NotFoundException) {
        throw error;
      }

      // Log the actual error for debugging
      this.logger.error(
        `Error retrieving chat ${chatId} in project ${projectId}:`,
        error,
      );

      // Type guard for Firebase errors
      const isFirebaseError = (err: any): err is { code: string } => {
        if (!err || typeof err !== 'object' || !('code' in err)) {
          return false;
        }
        return typeof (err as { code?: unknown }).code === 'string';
      };

      // Re-throw specific Firebase errors
      if (isFirebaseError(error) && error.code === 'permission-denied') {
        throw new UnauthorizedException(
          'Insufficient permissions to access chat',
        );
      }

      if (isFirebaseError(error) && error.code === 'unauthenticated') {
        throw new UnauthorizedException(
          'Authentication required to access chat',
        );
      }

      // For any other errors, throw a generic server error
      throw error;
    }
  }
}
