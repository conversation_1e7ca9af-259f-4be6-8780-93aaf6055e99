import { Modu<PERSON> } from '@nestjs/common';
import { RegisterModule } from './register/register.module';
import { Account } from './user/account/account';
import { Project } from './user/project/project';
import { WebhookModule } from './user/project/webhook/webhook.module';
import { ChatModule } from './user/project/chat/chat.module';
import { MessageModule } from './user/project/chat/message/message.module';

@Module({
  imports: [
    RegisterModule,
    Account,
    Project,
    WebhookModule,
    ChatModule,
    MessageModule,
  ],
  controllers: [],
  providers: [],
})
export class EndpointModule {}
