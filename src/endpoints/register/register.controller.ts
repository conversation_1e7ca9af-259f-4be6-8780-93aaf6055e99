import { Body, Controller, Post } from '@nestjs/common';
import { RegisterService } from './register.service';
import { RegisterDto } from './register.dto';

@Controller('register')
export class RegisterController {
  constructor(private readonly registerService: RegisterService) {}

  @Post()
  async register(@Body() body: RegisterDto) {
    const user = await this.registerService.register(body);
    return {
      message: 'User registered successfully',
      user,
    };
  }
}
