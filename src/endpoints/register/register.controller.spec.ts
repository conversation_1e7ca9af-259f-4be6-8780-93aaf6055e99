import { Test, TestingModule } from '@nestjs/testing';
import { RegisterController } from './register.controller';
import { RegisterModule } from './register.module';
import { FirebaseModule } from '../../commons/modules/firebase/firebase.module';
import { ConfigModule } from '@nestjs/config';

describe('RegisterController', () => {
  let controller: RegisterController;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      imports: [
        await ConfigModule.forRoot(),
        FirebaseModule.forRoot(),
        RegisterModule,
      ],
    }).compile();

    controller = module.get<RegisterController>(RegisterController);
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });

  describe('register', () => {
    it('should register user successfully', async () => {
      const body = {
        fullName: '<PERSON>',
        email: '<EMAIL>',
        password: 'password',
      };
      const result = await controller.register(body);
      expect(result).toBeDefined();
      expect(result.message).toBe('User registered successfully');
      expect(result.user).toBeDefined();
    });
  });
});
