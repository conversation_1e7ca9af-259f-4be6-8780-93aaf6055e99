import { Inject, Injectable } from '@nestjs/common';
import { app } from 'firebase-admin';
import { RegisterDto } from './register.dto';

@Injectable()
export class RegisterService {
  constructor(
    @Inject('FIREBASE_ADMIN') private readonly firebaseAdmin: app.App,
  ) {}

  async register(body: RegisterDto) {
    const { fullName, email, password } = body;

    const user = await this.firebaseAdmin.auth().createUser({
      email,
      password,
      displayName: fullName,
    });

    await this.firebaseAdmin.auth().setCustomUserClaims(user.uid, {
      type: 'client',
    });

    return user;
  }
}
