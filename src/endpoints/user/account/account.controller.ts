import { Controller, Get, UseGuards } from '@nestjs/common';
import { AccountService } from './account.service';
import { AuthGuard } from '../../../commons/guards/auth.guard';
import { CurrentUser } from '../../../commons/decorators/current-user.decorator';
import { auth } from 'firebase-admin';

@Controller('/user/account')
@UseGuards(AuthGuard)
export class AccountController {
  constructor(private readonly myAccountService: AccountService) {}

  @Get()
  getMyAccount(@CurrentUser() user: auth.UserRecord) {
    return {
      data: user,
    };
  }
}
