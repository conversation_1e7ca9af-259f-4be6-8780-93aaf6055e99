import { Inject, Injectable } from '@nestjs/common';
import { app, auth, firestore } from 'firebase-admin';
import { UpdateProjectDto } from './project.dto';
import { ProjectModel } from '../../../commons/firestore-models/project/project.model.interface';
import { ProjectModelFirestore } from '../../../commons/firestore-models/project/project.model.firestore';

@Injectable()
export class ProjectService {
  private projectCollection: firestore.CollectionReference<ProjectModel>;

  constructor(
    @Inject('FIREBASE_ADMIN') private readonly firebaseAdmin: app.App,
  ) {
    this.projectCollection = this.firebaseAdmin
      .firestore()
      .collection('projects')
      .withConverter(ProjectModelFirestore.converter());
  }

  async getAllProjects(user: auth.UserRecord) {
    const getProjects = await this.projectCollection
      .where('ownerUserUid', '==', user.uid)
      .withConverter(ProjectModelFirestore.converter())
      .get();

    const projects = getProjects.docs.map((doc) => doc.data());

    return { message: 'Successfully get all projects', data: projects };
  }

  getProjectById(project: ProjectModel) {
    return { message: 'Successfully get project', data: project };
  }

  async updateProject(
    project: ProjectModel,
    updateProjectDto: UpdateProjectDto,
  ) {
    await this.projectCollection.doc(project.uid).update({
      projectName: updateProjectDto.projectName,
      description: updateProjectDto.description,
      updatedAt: firestore.FieldValue.serverTimestamp(),
    });

    return {
      message: 'Successfully updated project',
      data: {
        ...updateProjectDto,
      },
    };
  }
}
