import {
  BadRequestException,
  Injectable,
  InternalServerErrorException,
} from '@nestjs/common';
import type { Express } from 'express';
import type { ProjectModel } from '../../../../../commons/firestore-models/project/project.model.interface';
import type { ChatModel } from '../../../../../commons/firestore-models/chat/chat.model.interface';
import { MetaWhatsappService } from '../../../../../commons/modules/meta/meta-whatsapp.service';
import type { UploadMediaPayloadDto } from './message.dto';

@Injectable()
export class MessageMediaService {
  constructor(private readonly metaWhatsappService: MetaWhatsappService) {}

  async uploadMedia(
    project: ProjectModel,
    _chat: ChatModel,
    file: Express.Multer.File | undefined,
    payload: UploadMediaPayloadDto,
  ) {
    if (!file) {
      throw new BadRequestException('Media file is required');
    }

    const credentials = project.whatsappCredentials;
    if (!credentials?.bearerToken || !credentials?.phoneNumberId) {
      throw new InternalServerErrorException(
        'WhatsApp credentials are not configured for this project',
      );
    }

    if (!file.mimetype) {
      throw new BadRequestException('Media file mime type is required');
    }

    if (!file.buffer?.length) {
      throw new BadRequestException('Media file content is empty');
    }

    const response = await this.metaWhatsappService.uploadMedia({
      file: file.buffer,
      filename: payload?.filename ?? file.originalname,
      mimeType: file.mimetype,
      caption: payload?.caption,
      accessToken: credentials.bearerToken,
      phoneNumberId: credentials.phoneNumberId,
    });

    return {
      message: 'Successfully uploaded media',
      data: response,
    };
  }
}
