import {
  Body,
  Controller,
  Get,
  Post,
  UploadedFile,
  UseGuards,
  UseInterceptors,
  Param,
  Delete,
  UsePipes,
} from '@nestjs/common';
import { ZodValidationPipe } from 'nestjs-zod';
import { MessageService } from './message.service';
import { AuthGuard } from '../../../../../commons/guards/auth.guard';
import { ProjectGuard } from '../../../../../commons/guards/project.guard';
import { ChatGuard } from '../../../../../commons/guards/chat.guard';
import { CurrentProject } from '../../../../../commons/decorators/project.decorator';
import { CurrentChat } from '../../../../../commons/decorators/chat.decorator';
import type { ProjectModel } from '../../../../../commons/firestore-models/project/project.model.interface';
import type { ChatModel } from '../../../../../commons/firestore-models/chat/chat.model.interface';
import * as messageDto from './message.dto';
import { auth } from 'firebase-admin';
import { CurrentUser } from '../../../../../commons/decorators/current-user.decorator';
import { MessageMediaService } from './message-media.service';
import { FileInterceptor } from '@nestjs/platform-express';
import { memoryStorage } from 'multer';
import type { Express } from 'express';

/**
 * MessageController demonstrates how to use layered guards and decorators:
 * 1. AuthGuard - Validates user authentication
 * 2. ProjectGuard - Validates project ownership
 * 3. ChatGuard - Validates chat existence within the project
 *
 * The guards are executed in order, each one building on the previous validation.
 * Data retrieved by guards is made available through corresponding decorators.
 */
@Controller('/user/project/:projectId/chat/:chatId')
@UseGuards(AuthGuard, ProjectGuard, ChatGuard)
export class MessageController {
  constructor(
    private readonly messageService: MessageService,
    private readonly messageMediaService: MessageMediaService,
  ) {}

  @Get('/messages')
  async getMessages(
    @CurrentProject() project: ProjectModel,
    @CurrentChat() chat: ChatModel,
  ) {
    return await this.messageService.getMessagesByChatId(project, chat);
  }

  @Post('/messages')
  async sendMessage(
    @CurrentProject() project: ProjectModel,
    @CurrentChat() chat: ChatModel,
    @Body() payload: messageDto.SendMessagePayloadDto,
    @CurrentUser() user: auth.UserRecord,
  ) {
    return await this.messageService.sendMessage(project, chat, payload, user);
  }

  @Post('/media')
  @UseInterceptors(
    FileInterceptor('file', {
      storage: memoryStorage(),
    }),
  )
  async uploadMedia(
    @CurrentProject() project: ProjectModel,
    @CurrentChat() chat: ChatModel,
    @UploadedFile() file: Express.Multer.File,
    @Body() payload: messageDto.UploadMediaPayloadDto,
  ) {
    return await this.messageMediaService.uploadMedia(
      project,
      chat,
      file,
      payload,
    );
  }

  @Get('/media/:mediaId')
  async getMedia(
    @CurrentProject() project: ProjectModel,
    @Param() params: messageDto.MediaIdParamDto,
  ) {
    return await this.messageService.getMedia(project, params.mediaId);
  }

  @Delete('/media/:mediaId')
  async deleteMedia(
    @CurrentProject() project: ProjectModel,
    @Param() params: messageDto.MediaIdParamDto,
  ) {
    return await this.messageService.deleteMedia(project, params.mediaId);
  }
}
