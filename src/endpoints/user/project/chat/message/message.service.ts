import {
  BadRequestException,
  Inject,
  Injectable,
  InternalServerErrorException,
} from '@nestjs/common';
import { app, auth, firestore } from 'firebase-admin';
import type { ProjectModel } from '../../../../../commons/firestore-models/project/project.model.interface';
import type { ChatModel } from '../../../../../commons/firestore-models/chat/chat.model.interface';
import { MessageModelFirestore } from '../../../../../commons/firestore-models/message/message-model.firestore';
import { MetaWhatsappService } from '../../../../../commons/modules/meta/meta-whatsapp.service';
import {
  MetaWhatsappMessage,
  MetaWhatsappSendMessageResponse,
} from '../../../../../commons/modules/meta/meta-whatsapp.interface';
import {
  RawMessageModel,
  TMessageType,
} from '../../../../../commons/firestore-models/message/message.model.interface';
import type {
  DeleteMediaResponseDto,
  GetMediaResponseDto,
} from './message.dto';
import { SendMessagePayloadDto } from './message.dto';

@Injectable()
export class MessageService {
  private projectCollection: firestore.CollectionReference;

  constructor(
    @Inject('FIREBASE_ADMIN') private readonly firebaseAdmin: app.App,
    private readonly metaWhatsappService: MetaWhatsappService,
  ) {
    this.projectCollection = this.firebaseAdmin
      .firestore()
      .collection('projects');
  }

  async getMessagesByChatId(project: ProjectModel, chat: ChatModel) {
    // Since ChatGuard has already validated the chat exists, we can skip the existence check
    // and directly access the messages collection

    // Now we can access the chat ID from the ChatModel
    const messagesCollection = this.projectCollection
      .doc(project.uid) // Project ID from the project object
      .collection('chats')
      .doc(chat.id) // Chat ID from the chat object (now available)
      .collection('messages');

    const messagesSnapshot = await messagesCollection
      .withConverter(MessageModelFirestore.converter())
      .get();

    const messagesData = messagesSnapshot.docs.map((doc) => doc.data());

    return {
      message: 'Successfully retrieved messages in chat',
      data: messagesData,
    };
  }

  async sendMessage(
    project: ProjectModel,
    chat: ChatModel,
    payload: SendMessagePayloadDto,
    user: auth.UserRecord,
  ) {
    const credentials = project.whatsappCredentials;

    if (!credentials?.bearerToken || !credentials?.phoneNumberId) {
      throw new InternalServerErrorException(
        'WhatsApp credentials are not configured for this project',
      );
    }

    const { metaMessage, firestoreMessagePayload } =
      this.transformPayload(payload);

    let sendResponse: MetaWhatsappSendMessageResponse;
    try {
      sendResponse = await this.metaWhatsappService.sendMessage({
        to: chat.phoneNumber,
        message: metaMessage,
        accessToken: credentials.bearerToken,
        phoneNumberId: credentials.phoneNumberId,
      });
    } catch (error) {
      throw new InternalServerErrorException(
        'Failed to send WhatsApp message',
        {
          cause: error instanceof Error ? error : undefined,
          description: 'Meta WhatsApp sendMessage call failed',
        },
      );
    }

    const now = new Date();
    const messageId =
      sendResponse.messages?.[0]?.id ??
      this.firebaseAdmin.firestore().collection('projects').doc().id;

    const rawMessage = {
      message: {
        id: messageId,
        from: credentials.phoneNumber,
        timestamp: now,
        type: payload.type as TMessageType,
        context: null,
        ...firestoreMessagePayload,
      },
      statuses: null,
      direction: 'out',
      createdAt: now,
      sentBy: {
        uid: user.uid,
        email: user.email || 'Unknown',
        name: user.displayName || 'Unknown',
      },
    } as unknown as RawMessageModel<Date>;

    const projectDoc = this.projectCollection.doc(project.uid);
    const chatDoc = projectDoc.collection('chats').doc(chat.id);
    const messageDoc = chatDoc.collection('messages').doc(messageId);

    const batch = this.firebaseAdmin.firestore().batch();

    batch.set(messageDoc, rawMessage);
    batch.update(chatDoc, {
      updatedAt: now,
      lastMessage: rawMessage,
    });

    await batch.commit();

    return {
      message: 'Successfully sent message',
      data: rawMessage,
    };
  }

  private transformPayload(payload: SendMessagePayloadDto): {
    metaMessage: MetaWhatsappMessage;
    firestoreMessagePayload: Record<string, unknown>;
  } {
    const normalizedPayload = payload;

    switch (normalizedPayload.type) {
      case 'text': {
        const body = normalizedPayload.text?.body;
        if (!body) {
          throw new BadRequestException('Text body is required');
        }

        const previewUrl = normalizedPayload.text?.previewUrl;

        return {
          metaMessage: {
            type: 'text',
            text: {
              body,
              ...(previewUrl !== undefined ? { preview_url: previewUrl } : {}),
            },
          },
          firestoreMessagePayload: {
            text: {
              body,
            },
          },
        };
      }
      case 'image': {
        const image = normalizedPayload.image;
        if (!image?.id && !image?.link) {
          throw new BadRequestException('Image id or link is required');
        }

        return {
          metaMessage: {
            type: 'image',
            image: {
              ...(image.id ? { id: image.id } : {}),
              ...(image.link ? { link: image.link } : {}),
              ...(image.caption ? { caption: image.caption } : {}),
            },
          },
          firestoreMessagePayload: {
            image: {
              id: image.id ?? '',
              mime_type: '',
              sha256: '',
              caption: image.caption,
            },
          },
        };
      }
      case 'audio': {
        const audio = normalizedPayload.audio;
        if (!audio?.id && !audio?.link) {
          throw new BadRequestException('Audio id or link is required');
        }

        return {
          metaMessage: {
            type: 'audio',
            audio: {
              ...(audio.id ? { id: audio.id } : {}),
              ...(audio.link ? { link: audio.link } : {}),
            },
          },
          firestoreMessagePayload: {
            audio: {
              id: audio.id ?? '',
              mime_type: '',
              sha256: '',
            },
          },
        };
      }
      case 'video': {
        const video = normalizedPayload.video;
        if (!video?.id && !video?.link) {
          throw new BadRequestException('Video id or link is required');
        }

        return {
          metaMessage: {
            type: 'video',
            video: {
              ...(video.id ? { id: video.id } : {}),
              ...(video.link ? { link: video.link } : {}),
              ...(video.caption ? { caption: video.caption } : {}),
            },
          },
          firestoreMessagePayload: {
            video: {
              id: video.id ?? '',
              mime_type: '',
              sha256: '',
              caption: video.caption,
            },
          },
        };
      }
      case 'document': {
        const document = normalizedPayload.document;
        if (!document?.id && !document?.link) {
          throw new BadRequestException('Document id or link is required');
        }

        return {
          metaMessage: {
            type: 'document',
            document: {
              ...(document.id ? { id: document.id } : {}),
              ...(document.link ? { link: document.link } : {}),
              ...(document.caption ? { caption: document.caption } : {}),
              ...(document.filename ? { filename: document.filename } : {}),
            },
          },
          firestoreMessagePayload: {
            document: {
              id: document.id ?? '',
              mime_type: '',
              sha256: '',
              caption: document.caption,
            },
          },
        };
      }
      case 'interactive': {
        const interactive = normalizedPayload.interactive;
        if (!interactive) {
          throw new BadRequestException('Interactive payload is required');
        }

        return {
          metaMessage: {
            type: 'interactive',
            interactive,
          },
          firestoreMessagePayload: {
            interactive,
          },
        };
      }
      case 'template': {
        const template = normalizedPayload.template;
        if (!template) {
          throw new BadRequestException('Template payload is required');
        }

        return {
          metaMessage: {
            type: 'template',
            template,
          },
          firestoreMessagePayload: {
            template,
          },
        };
      }
      default:
        throw new BadRequestException(
          `Unsupported message type: ${payload.type}`,
        );
    }
  }

  async getMedia(
    project: ProjectModel,
    mediaId: string,
  ): Promise<GetMediaResponseDto> {
    const credentials = project.whatsappCredentials;

    if (!credentials?.bearerToken) {
      throw new InternalServerErrorException(
        'WhatsApp credentials are not configured for this project',
      );
    }

    const response = await this.metaWhatsappService.getMedia(
      mediaId,
      credentials.bearerToken,
    );

    return {
      message: 'Successfully retrieved media information',
      data: {
        messaging_product: response.messaging_product,
        url: response.url,
        mime_type: response.mime_type,
        sha256: response.sha256,
        file_size: response.file_size,
        id: response.id,
      },
    };
  }

  async deleteMedia(
    project: ProjectModel,
    mediaId: string,
  ): Promise<DeleteMediaResponseDto> {
    const credentials = project.whatsappCredentials;

    if (!credentials?.bearerToken) {
      throw new InternalServerErrorException(
        'WhatsApp credentials are not configured for this project',
      );
    }

    const response = await this.metaWhatsappService.deleteMedia(
      mediaId,
      credentials.bearerToken,
    );

    return {
      message: 'Successfully deleted media',
      data: {
        success: response.success,
      },
    };
  }
}
