import { z } from 'zod';

const mediaPayloadSchema = z
  .object({
    id: z.string().min(1, 'ID is required').optional(),
    link: z.string().min(1, 'Link is required').optional(),
    caption: z.string().optional(),
    filename: z.string().optional(),
  })
  .refine((val) => Boolean(val.id || val.link), {
    message: 'ID or link is required',
  });

const SendMessagePayload_TextSchema = z.object({
  type: z.literal('text'),
  text: z.object({
    body: z.string().min(1, 'Body is required'),
    previewUrl: z.boolean().optional(),
  }),
});

const SendMessagePayload_ImageSchema = z.object({
  type: z.literal('image'),
  image: mediaPayloadSchema,
});

const SendMessagePayload_AudioSchema = z.object({
  type: z.literal('audio'),
  audio: mediaPayloadSchema,
});

const SendMessagePayload_VideoSchema = z.object({
  type: z.literal('video'),
  video: mediaPayloadSchema,
});

const SendMessagePayload_DocumentSchema = z.object({
  type: z.literal('document'),
  document: mediaPayloadSchema,
});

const SendMessagePayload_InteractiveSchema = z.object({
  type: z.literal('interactive'),
  interactive: z.object({
    type: z.string().min(1, 'Type is required'),
    header: z.object().optional(),
    body: z.object(),
    footer: z.object().optional(),
    action: z.object(),
  }),
});

const SendMessagePayload_TemplateSchema = z.object({
  type: z.literal('template'),
  template: z.object({
    name: z.string().min(1, 'Name is required'),
    language: z.object({
      code: z.string().min(1, 'Code is required'),
      policy: z.string().optional(),
    }),
    components: z.array(z.object()).optional(),
  }),
});

const MapSendMessagePayloadSchema = [
  SendMessagePayload_TextSchema,
  SendMessagePayload_ImageSchema,
  SendMessagePayload_AudioSchema,
  SendMessagePayload_VideoSchema,
  SendMessagePayload_DocumentSchema,
  SendMessagePayload_InteractiveSchema,
  SendMessagePayload_TemplateSchema,
] as const;

// eslint-disable-next-line @typescript-eslint/no-unused-vars
const DiscriminatedSendMessagePayloadSchema = z.discriminatedUnion(
  'type',
  MapSendMessagePayloadSchema,
);

export type SendMessagePayloadDto = z.infer<
  typeof DiscriminatedSendMessagePayloadSchema
>;

export const UploadMediaPayloadSchema = z.object({
  caption: z.string().optional(),
  filename: z.string().optional(),
});

export type UploadMediaPayloadDto = z.infer<typeof UploadMediaPayloadSchema>;

// DTO for validating media ID parameter
export const MediaIdParamSchema = z.object({
  mediaId: z.string().min(1, 'Media ID is required'),
});

export type MediaIdParamDto = z.infer<typeof MediaIdParamSchema>;

// DTO for get media response
export type GetMediaResponseDto = {
  message: string;
  data: {
    messaging_product: 'whatsapp';
    url: string;
    mime_type: string;
    sha256: string;
    file_size: string;
    id: string;
  };
};

// DTO for delete media response
export type DeleteMediaResponseDto = {
  message: string;
  data: {
    success: boolean;
  };
};
