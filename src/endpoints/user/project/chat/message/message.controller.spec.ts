import { Test, TestingModule } from '@nestjs/testing';
import { MessageController } from './message.controller';
import { app } from 'firebase-admin';
import { ProjectModel } from '../../../../../commons/firestore-models/project/project.model.interface';
import { ProjectModelFirestore } from '../../../../../commons/firestore-models/project/project.model.firestore';
import { FirebaseModule } from '../../../../../commons/modules/firebase/firebase.module';
import { ConfigModule } from '@nestjs/config';
import { MessageModule } from './message.module';
import { ChatModel } from '../../../../../commons/firestore-models/chat/chat.model.interface';
import { ChatModelFirestore } from '../../../../../commons/firestore-models/chat/chat.model.firestore';

describe('MessageController', () => {
  let controller: MessageController;
  let firebaseAdmin: app.App;
  let projectId: string;
  let project: ProjectModel;
  let chatId: string;
  let chat: ChatModel;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      imports: [
        await ConfigModule.forRoot({
          envFilePath: '.env',
        }),
        FirebaseModule.forRoot(),
        MessageModule,
      ],
    }).compile();

    controller = module.get<MessageController>(MessageController);
    firebaseAdmin = module.get<app.App>('FIREBASE_ADMIN');
    projectId = process.env.TEST_PROJECT_ID || '123';
    chatId = process.env.TEST_PHONE_NUMBER_TARGET || '123';

    // Get a real project from Firebase for testing
    const getProject = await firebaseAdmin
      .firestore()
      .collection('projects')
      .doc(projectId)
      .withConverter(ProjectModelFirestore.converter())
      .get();

    if (!getProject.exists) {
      throw new Error('Project not found');
    }

    project = getProject.data()!;

    // Get a real chat from Firebase for testing
    const getChat = await firebaseAdmin
      .firestore()
      .collection('projects')
      .doc(projectId)
      .collection('chats')
      .doc(chatId)
      .withConverter(ChatModelFirestore.converter())
      .get();

    if (!getChat.exists) {
      throw new Error('Chat not found');
    }

    chat = getChat.data()!;
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });

  it('should get messages with comprehensive data validation', async () => {
    const result = await controller.getMessages(project, chat);

    // Basic response structure validation
    expect(result).toHaveProperty('message');
    expect(result).toHaveProperty('data');
    expect(typeof result.message).toBe('string');
    expect(Array.isArray(result.data)).toBe(true);
  });
});
