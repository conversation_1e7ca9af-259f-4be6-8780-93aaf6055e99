import { Modu<PERSON> } from '@nestjs/common';
import { MessageService } from './message.service';
import { MessageController } from './message.controller';
import { MetaWhatsappModule } from '../../../../../commons/modules/meta/meta-whatsapp.module';
import { MessageMediaService } from './message-media.service';

@Module({
  imports: [MetaWhatsappModule],
  controllers: [MessageController],
  providers: [MessageService, MessageMediaService],
  exports: [MessageService],
})
export class MessageModule {}
