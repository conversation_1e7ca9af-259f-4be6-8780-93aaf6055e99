import { Inject, Injectable, NotFoundException } from '@nestjs/common';
import { app, firestore } from 'firebase-admin';
import { ProjectModel } from '../../../../commons/firestore-models/project/project.model.interface';
import { ProjectModelFirestore } from '../../../../commons/firestore-models/project/project.model.firestore';
import { ChatModelFirestore } from '../../../../commons/firestore-models/chat/chat.model.firestore';

@Injectable()
export class ChatService {
  private projectCollection: firestore.CollectionReference<ProjectModel>;
  constructor(
    @Inject('FIREBASE_ADMIN') private readonly firebaseAdmin: app.App,
  ) {
    this.projectCollection = this.firebaseAdmin
      .firestore()
      .collection('projects')
      .withConverter(ProjectModelFirestore.converter());
  }

  async getChats(project: ProjectModel) {
    const chatsCollection = this.projectCollection
      .doc(project.uid)
      .collection('chats');

    const getChats = await chatsCollection
      .withConverter(ChatModelFirestore.converter())
      .get();

    const chats = getChats.docs.map((doc) => doc.data());

    return { message: 'Successfully get all chats', data: chats };
  }

  async getChat(chatId: string, project: ProjectModel) {
    const chatsCollection = this.projectCollection
      .doc(project.uid)
      .collection('chats');

    const getChat = await chatsCollection
      .withConverter(ChatModelFirestore.converter())
      .doc(chatId)
      .get();

    if (!getChat.exists) {
      throw new NotFoundException('Chat not found');
    }

    return { message: 'Successfully get chat', data: getChat.data()! };
  }
}
