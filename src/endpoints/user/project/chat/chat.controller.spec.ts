import { Test, TestingModule } from '@nestjs/testing';
import { Chat<PERSON>ontroller } from './chat.controller';
import { app } from 'firebase-admin';
import { ConfigModule } from '@nestjs/config';
import { FirebaseModule } from '../../../../commons/modules/firebase/firebase.module';
import { ChatModule } from './chat.module';
import { ProjectModel } from '../../../../commons/firestore-models/project/project.model.interface';
import { ProjectModelFirestore } from '../../../../commons/firestore-models/project/project.model.firestore';

describe('ChatController', () => {
  let controller: ChatController;
  let firebaseAdmin: app.App;
  let projectId: string;
  let project: ProjectModel;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      imports: [
        await ConfigModule.forRoot({
          envFilePath: '.env',
        }),
        FirebaseModule.forRoot(),
        ChatModule,
      ],
    }).compile();

    controller = module.get<ChatController>(ChatController);
    firebaseAdmin = module.get<app.App>('FIREBASE_ADMIN');
    projectId = process.env.TEST_PROJECT_ID || '123';
    const getProject = await firebaseAdmin
      .firestore()
      .collection('projects')
      .doc(projectId)
      .withConverter(ProjectModelFirestore.converter())
      .get();

    if (!getProject.exists) {
      throw new Error('Project not found');
    }

    project = getProject.data()!;
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });

  it('should get chats with comprehensive data validation', async () => {
    const result = await controller.getChats(project);

    // Basic response structure validation
    expect(result).toHaveProperty('message');
    expect(result).toHaveProperty('data');
    expect(typeof result.message).toBe('string');
    expect(Array.isArray(result.data)).toBe(true);

    // Verify that we get at least one chat (as per requirement)
    expect(result.data.length).toBeGreaterThan(0);
  });
});
