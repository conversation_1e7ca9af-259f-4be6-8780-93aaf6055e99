import { Controller, Get, UseGuards } from '@nestjs/common';
import { ChatService } from './chat.service';
import { AuthGuard } from '../../../../commons/guards/auth.guard';
import { ProjectGuard } from '../../../../commons/guards/project.guard';
import { CurrentProject } from '../../../../commons/decorators/project.decorator';
import type { ProjectModel } from '../../../../commons/firestore-models/project/project.model.interface';

@Controller('/user/project/:projectId')
@UseGuards(AuthGuard, ProjectGuard)
export class ChatController {
  constructor(private readonly chatService: ChatService) {}

  @Get('/chat')
  getChats(@CurrentProject() project: ProjectModel) {
    return this.chatService.getChats(project);
  }
}
