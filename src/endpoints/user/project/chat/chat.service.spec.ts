import { Test, TestingModule } from '@nestjs/testing';
import { ChatService } from './chat.service';
import { FirebaseModule } from '../../../../commons/modules/firebase/firebase.module';
import { ConfigModule } from '@nestjs/config';
import { ProjectModel } from '../../../../commons/firestore-models/project/project.model.interface';
import { app } from 'firebase-admin';
import { ProjectModelFirestore } from '../../../../commons/firestore-models/project/project.model.firestore';

describe('ChatService', () => {
  let service: ChatService;
  let firebaseAdmin: app.App;
  let projectId: string;
  let project: ProjectModel;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      imports: [
        await ConfigModule.forRoot({
          envFilePath: '.env',
        }),
        FirebaseModule.forRoot(),
      ],
      providers: [ChatService],
    }).compile();

    service = module.get<ChatService>(ChatService);
    projectId = process.env.TEST_PROJECT_ID || '123';
    firebaseAdmin = module.get<app.App>('FIREBASE_ADMIN');

    const getProject = await firebaseAdmin
      .firestore()
      .collection('projects')
      .doc(projectId)
      .withConverter(ProjectModelFirestore.converter())
      .get();

    if (!getProject.exists) {
      throw new Error('Project not found');
    }

    project = getProject.data()!;
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  it('should get chats with comprehensive data validation', async () => {
    const result = await service.getChats(project);

    // Basic response structure validation
    expect(result).toHaveProperty('message');
    expect(result).toHaveProperty('data');
    expect(typeof result.message).toBe('string');
    expect(Array.isArray(result.data)).toBe(true);

    // Verify that we get at least one chat (as per requirement)
    expect(result.data.length).toBeGreaterThan(0);

    // Detailed validation of chat data structure
    const firstChat = result.data[0];

    // Validate basic chat properties
    expect(firstChat).toHaveProperty('phoneNumber');
    expect(firstChat).toHaveProperty('name');
    expect(firstChat).toHaveProperty('createdAt');
    expect(firstChat).toHaveProperty('updatedAt');
    expect(firstChat).toHaveProperty('lastMessage');

    // Validate data types
    expect(typeof firstChat.phoneNumber).toBe('string');
    expect(typeof firstChat.name).toBe('string');
    expect(firstChat.createdAt).toBeInstanceOf(Date);
    expect(firstChat.updatedAt).toBeInstanceOf(Date);

    // Validate that phoneNumber is not empty
    expect(firstChat.phoneNumber).not.toBe('');
    expect(firstChat.phoneNumber).not.toBeNull();
    expect(firstChat.phoneNumber).not.toBeUndefined();

    // Validate that name is not empty
    expect(firstChat.name).not.toBe('');
    expect(firstChat.name).not.toBeNull();
    expect(firstChat.name).not.toBeUndefined();

    // Validate date properties
    expect(firstChat.createdAt.getTime()).not.toBeNaN();
    expect(firstChat.updatedAt.getTime()).not.toBeNaN();

    // Validate lastMessage structure if it exists
    if (firstChat.lastMessage !== null) {
      expect(firstChat.lastMessage).toHaveProperty('message');
      expect(firstChat.lastMessage).toHaveProperty('statuses');
      expect(firstChat.lastMessage).toHaveProperty('direction');
      expect(firstChat.lastMessage).toHaveProperty('createdAt');

      // Validate lastMessage.message structure
      expect(firstChat.lastMessage.message).toHaveProperty('id');
      expect(firstChat.lastMessage.message).toHaveProperty('from');
      expect(firstChat.lastMessage.message).toHaveProperty('timestamp');
      expect(firstChat.lastMessage.message).toHaveProperty('type');

      // Validate lastMessage data types
      expect(typeof firstChat.lastMessage.message.id).toBe('string');
      expect(typeof firstChat.lastMessage.message.from).toBe('string');
      expect(firstChat.lastMessage.message.timestamp).toBeInstanceOf(Date);
      expect(typeof firstChat.lastMessage.message.type).toBe('string');

      // Validate lastMessage direction
      expect(firstChat.lastMessage.direction).toMatch(/^(in|out)$/);

      // Validate lastMessage createdAt
      expect(firstChat.lastMessage.createdAt).toBeInstanceOf(Date);
      expect(firstChat.lastMessage.createdAt.getTime()).not.toBeNaN();
    }

    // Validate that all chats in the array have the same structure
    result.data.forEach((chat) => {
      expect(chat).toHaveProperty('phoneNumber');
      expect(chat).toHaveProperty('name');
      expect(chat).toHaveProperty('createdAt');
      expect(chat).toHaveProperty('updatedAt');
      expect(chat).toHaveProperty('lastMessage');

      expect(typeof chat.phoneNumber).toBe('string');
      expect(typeof chat.name).toBe('string');
      expect(chat.createdAt).toBeInstanceOf(Date);
      expect(chat.updatedAt).toBeInstanceOf(Date);

      expect(chat.phoneNumber).not.toBe('');
      expect(chat.name).not.toBe('');

      expect(chat.createdAt.getTime()).not.toBeNaN();
      expect(chat.updatedAt.getTime()).not.toBeNaN();
    });
  });

  it('should get specific chat by chatId', async () => {
    // First get all chats to ensure we have data
    const allChats = await service.getChats(project);
    expect(allChats.data.length).toBeGreaterThan(0);

    // Use the first chat's phoneNumber as chatId (based on service implementation)
    const chatId = allChats.data[0].phoneNumber;

    const result = await service.getChat(chatId, project);

    // Basic response structure validation
    expect(result).toHaveProperty('message');
    expect(result).toHaveProperty('data');
    expect(typeof result.message).toBe('string');
    expect(result.message).toBe('Successfully get chat');

    // Validate chat data structure
    const chat = result.data;
    expect(chat).toHaveProperty('phoneNumber');
    expect(chat).toHaveProperty('name');
    expect(chat).toHaveProperty('createdAt');
    expect(chat).toHaveProperty('updatedAt');
    expect(chat).toHaveProperty('lastMessage');

    // Validate data types
    expect(typeof chat.phoneNumber).toBe('string');
    expect(typeof chat.name).toBe('string');
    expect(chat.createdAt).toBeInstanceOf(Date);
    expect(chat.updatedAt).toBeInstanceOf(Date);

    // Validate that we got the correct chat
    expect(chat.phoneNumber).toBe(chatId);
  });

  it('should throw NotFoundException when chat does not exist', async () => {
    const nonExistentChatId = 'non-existent-chat-id';

    await expect(service.getChat(nonExistentChatId, project)).rejects.toThrow(
      'Chat not found',
    );
  });
});
