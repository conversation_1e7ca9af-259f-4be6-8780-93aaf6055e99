import { Body, Controller, Get, Put, UseGuards } from '@nestjs/common';
import { ProjectService } from './project.service';
import { AuthGuard } from '../../../commons/guards/auth.guard';
import { CurrentUser } from '../../../commons/decorators/current-user.decorator';
import { auth } from 'firebase-admin';
import { UpdateProjectDto } from './project.dto';
import { ProjectGuard } from '../../../commons/guards/project.guard';
import { CurrentProject } from '../../../commons/decorators/project.decorator';
import type { ProjectModel } from '../../../commons/firestore-models/project/project.model.interface';

@Controller('/user/project')
@UseGuards(AuthGuard)
export class ProjectController {
  constructor(private readonly projectService: ProjectService) {}

  @Get()
  getAllProjects(@CurrentUser() user: auth.UserRecord) {
    return this.projectService.getAllProjects(user);
  }

  @Get(':projectId')
  @UseGuards(ProjectGuard)
  getProjectById(@CurrentProject() project: ProjectModel) {
    return this.projectService.getProjectById(project);
  }

  @Put(':projectId')
  @UseGuards(ProjectGuard)
  updateProject(
    @Body() updateProjectDto: UpdateProjectDto,
    @CurrentProject() project: ProjectModel,
  ) {
    return this.projectService.updateProject(project, updateProjectDto);
  }
}
