import { Inject, Injectable, NestMiddleware } from '@nestjs/common';
import { NextFunction, Request, Response } from 'express';
import { app } from 'firebase-admin';
import { WebhookPayload } from './webhook.dto';
import dayjs from 'dayjs';

@Injectable()
export class WebhookMiddleware implements NestMiddleware {
  constructor(
    @Inject('FIREBASE_ADMIN') private readonly firebaseAdmin: app.App,
  ) {}

  use(
    req: Request<{ projectId: string }, any, WebhookPayload>,
    res: Response,
    next: NextFunction,
  ) {
    const projectId = req.params.projectId;

    const projectDoc = this.firebaseAdmin
      .firestore()
      .collection('projects')
      .doc(projectId);

    const value = req.body.entry[0].changes[0].value;

    let messageId = '';
    let timestamp = '';
    let webhookType = 'message';
    let phoneNumber = '';

    // If Message
    if (value.messages) {
      messageId = value.messages[0].id;
      timestamp = value.messages[0].timestamp;
      phoneNumber = value.messages[0].from;
    }
    // If Status
    let status: string = 'unknown';
    if (value.statuses) {
      messageId = value.statuses[0].id;
      timestamp = value.statuses[0].timestamp;
      webhookType = 'status';
      status = value.statuses[0].status;
      phoneNumber = value.statuses[0].recipient_id;
    }

    const date = dayjs.unix(Number(timestamp)).toDate();
    const createdAt = new Date();

    const id = `${webhookType}__${status}__${messageId}`;

    projectDoc
      .collection('meta_webhooks')
      .doc(id)
      .set({
        messageId,
        original: req.body,
        originalStringify: JSON.stringify(req.body),
        date,
        createdAt,
        webhookType: webhookType,
        phoneNumber,
        status,
      })
      .then()
      .catch((reason) => {
        console.error('Error logging webhook request:', reason);
      });

    next();
  }
}
