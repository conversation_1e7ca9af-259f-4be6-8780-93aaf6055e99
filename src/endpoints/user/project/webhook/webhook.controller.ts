import { Body, Controller, Get, Param, Post, Query } from '@nestjs/common';
import { WebhookService } from './webhook.service';
import * as webhookDto from './webhook.dto';

@Controller('/user/project/:projectId/webhook')
export class WebhookController {
  constructor(private readonly webhookService: WebhookService) {}

  @Get()
  getWebhook(
    @Query() query: webhookDto.VerifyWebhookQueryDto,
    @Param('projectId') projectId: string,
  ) {
    return this.webhookService.verifyWebhook(projectId, query);
  }

  @Post()
  handleWebhook(
    @Param('projectId') projectId: string,
    @Body() body: webhookDto.WebhookPayloadDto,
  ) {
    return this.webhookService.handleWebhook(projectId, body);
  }
}
