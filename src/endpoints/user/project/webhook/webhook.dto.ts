import { z } from 'zod';
import { createZodDto } from 'nestjs-zod';

const VerifyWebhookSchemaQuery = z.object({
  'hub.mode': z.string().min(3),
  'hub.verify_token': z.string().min(3),
  'hub.challenge': z.string().min(3),
});

export class VerifyWebhookQueryDto extends createZodDto(
  VerifyWebhookSchemaQuery,
) {}

const ErrorSchema = z
  .object({
    code: z.number(),
    title: z.string(),
    message: z.string(),
    error_data: z
      .object({
        details: z.string(),
      })
      .optional(),
  })
  .loose();

const ContextSchema = z
  .object({
    forwarded: z.boolean().optional(),
    frequently_forwarded: z.boolean().optional(),
    from: z.string().optional(),
    id: z.string().optional(),
    referred_product: z.looseObject({}).optional(),
  })
  .loose();

const ReferralSchema = z
  .object({
    source_type: z.string(),
    source_id: z.string().optional(),
    source_url: z.string().optional(),
    body: z.string().optional(),
    headline: z.string().optional(),
    media_type: z.string().optional(),
    image_url: z.string().optional(),
    video_url: z.string().optional(),
    thumbnail_url: z.string().optional(),
    ctwa_clid: z.string().optional(),
    welcome_message: z
      .object({
        text: z.string(),
      })
      .optional(),
  })
  .loose();

const MetadataSchema = z.object({
  display_phone_number: z.string(),
  phone_number_id: z.string(),
});

const ContactSchema = z
  .object({
    profile: z
      .object({
        name: z.string().optional(),
      })
      .loose(),
    wa_id: z.string(),
  })
  .loose();

const AddressSchema = z
  .object({
    city: z.string().optional(),
    country: z.string().optional(),
    country_code: z.string().optional(),
    state: z.string().optional(),
    street: z.string().optional(),
    type: z.string().optional(),
    zip: z.string().optional(),
  })
  .loose();

const EmailSchema = z
  .object({
    email: z.string().optional(),
    type: z.string().optional(),
  })
  .loose();

const ContactNameSchema = z
  .object({
    formatted_name: z.string(),
    first_name: z.string().optional(),
    last_name: z.string().optional(),
    middle_name: z.string().optional(),
    suffix: z.string().optional(),
    prefix: z.string().optional(),
  })
  .loose();

const OrgSchema = z
  .object({
    company: z.string().optional(),
    department: z.string().optional(),
    title: z.string().optional(),
  })
  .loose();

const PhoneSchema = z
  .object({
    phone: z.string().optional(),
    type: z.string().optional(),
    wa_id: z.string().optional(),
  })
  .loose();

const UrlSchema = z
  .object({
    url: z.string().optional(),
    type: z.string().optional(),
  })
  .loose();

const ContactMessageSchema = z
  .object({
    addresses: z.array(AddressSchema).optional(),
    birthday: z.string().optional(),
    emails: z.array(EmailSchema).optional(),
    name: ContactNameSchema,
    org: OrgSchema.optional(),
    phones: z.array(PhoneSchema).optional(),
    urls: z.array(UrlSchema).optional(),
  })
  .loose();

const BaseMessageSchema = z
  .object({
    from: z.string(),
    id: z.string(),
    timestamp: z.string(),
    context: ContextSchema.optional(),
    referral: ReferralSchema.optional(),
    errors: z.array(ErrorSchema).optional(),
  })
  .loose();

const createMessageSchema = <Type extends string>(
  type: Type,
  shape: Record<string, z.ZodTypeAny>,
) =>
  BaseMessageSchema.extend({
    type: z.literal(type),
    ...shape,
  });

const MessageSchemaMap = {
  text: createMessageSchema('text', {
    text: z.object({
      body: z.string(),
    }),
  }),
  image: createMessageSchema('image', {
    image: z.object({
      id: z.string(),
      mime_type: z.string(),
      sha256: z.string(),
      caption: z.string().optional(),
    }),
  }),
  audio: createMessageSchema('audio', {
    audio: z.object({
      id: z.string(),
      mime_type: z.string(),
      sha256: z.string(),
      voice: z.boolean().optional(),
    }),
  }),
  video: createMessageSchema('video', {
    video: z.object({
      id: z.string(),
      mime_type: z.string(),
      sha256: z.string(),
      caption: z.string().optional(),
    }),
  }),
  document: createMessageSchema('document', {
    document: z.object({
      id: z.string(),
      mime_type: z.string(),
      sha256: z.string(),
      filename: z.string(),
      caption: z.string().optional(),
    }),
  }),
  sticker: createMessageSchema('sticker', {
    sticker: z.object({
      id: z.string(),
      mime_type: z.string(),
      sha256: z.string(),
    }),
  }),
  location: createMessageSchema('location', {
    location: z.object({
      latitude: z.string(),
      longitude: z.string(),
      name: z.string().optional(),
      address: z.string().optional(),
    }),
  }),
  contacts: createMessageSchema('contacts', {
    contacts: z.array(ContactMessageSchema),
  }),
  reaction: createMessageSchema('reaction', {
    reaction: z.object({
      message_id: z.string(),
      emoji: z.string(),
    }),
  }),
  interactive: createMessageSchema('interactive', {
    interactive: z
      .object({
        type: z.string(),
      })
      .loose(),
  }),
  button: createMessageSchema('button', {
    button: z.object({
      payload: z.string(),
      text: z.string(),
    }),
  }),
  order: createMessageSchema('order', {
    order: z.object({
      catalog_id: z.string(),
      text: z.string().optional(),
      product_items: z.array(
        z.object({
          product_retailer_id: z.string(),
          quantity: z.string(),
          item_price: z.string(),
          currency: z.string(),
        }),
      ),
    }),
  }),
  system: createMessageSchema('system', {
    system: z
      .object({
        type: z.string(),
        body: z.string(),
      })
      .loose(),
  }),
  unsupported: createMessageSchema('unsupported', {
    errors: z.array(ErrorSchema),
  }),
} as const;

const MessageSchemas = [
  MessageSchemaMap.text,
  MessageSchemaMap.image,
  MessageSchemaMap.audio,
  MessageSchemaMap.video,
  MessageSchemaMap.document,
  MessageSchemaMap.sticker,
  MessageSchemaMap.location,
  MessageSchemaMap.contacts,
  MessageSchemaMap.reaction,
  MessageSchemaMap.interactive,
  MessageSchemaMap.button,
  MessageSchemaMap.order,
  MessageSchemaMap.system,
  MessageSchemaMap.unsupported,
] as const;

const MessageSchema = z.discriminatedUnion('type', MessageSchemas);

const StatusSchema = z
  .object({
    id: z.string(),
    status: z.enum(['sent', 'delivered', 'read', 'failed']),
    timestamp: z.string(),
    recipient_id: z.string(),
    conversation: z
      .object({
        id: z.string(),
        expiration_timestamp: z.string().optional(),
        origin: z.object({
          type: z.string(),
        }),
      })
      .optional(),
    pricing: z
      .object({
        billable: z.boolean(),
        pricing_model: z.string(),
        category: z.string(),
      })
      .optional(),
    errors: z.array(ErrorSchema).optional(),
  })
  .loose();

const WebhookValueSchema = z
  .object({
    messaging_product: z.literal('whatsapp'),
    metadata: MetadataSchema,
    contacts: z.array(ContactSchema).optional(),
    messages: z.array(MessageSchema).optional(),
    statuses: z.array(StatusSchema).optional(),
  })
  .loose()
  .refine((value) => {
    const hasMessages =
      Array.isArray(value.messages) && value.messages.length > 0;
    const hasStatuses =
      Array.isArray(value.statuses) && value.statuses.length > 0;

    return hasMessages || hasStatuses;
  }, 'Either messages or statuses must be provided for WhatsApp webhooks.');

const WebhookChangeSchema = z
  .object({
    value: WebhookValueSchema,
    field: z.literal('messages'),
  })
  .loose();

const WebhookEntrySchema = z
  .object({
    id: z.string(),
    changes: z.array(WebhookChangeSchema),
  })
  .loose();

const WebhookPayloadSchema = z.object({
  object: z.literal('whatsapp_business_account'),
  entry: z.array(WebhookEntrySchema),
});

export class WebhookPayloadDto extends createZodDto(WebhookPayloadSchema) {}

export type WebhookPayload = z.infer<typeof WebhookPayloadSchema>;
export type WebhookEntry = z.infer<typeof WebhookEntrySchema>;
export type WebhookChange = z.infer<typeof WebhookChangeSchema>;
export type WebhookValue = z.infer<typeof WebhookValueSchema>;
export type WebhookContact = z.infer<typeof ContactSchema>;
export type WebhookMessage = z.infer<typeof MessageSchema>;
export type WebhookStatus = z.infer<typeof StatusSchema>;
export type WebhookError = z.infer<typeof ErrorSchema>;
