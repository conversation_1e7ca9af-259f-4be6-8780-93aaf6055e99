import {
  HttpException,
  Inject,
  Injectable,
  Logger,
  NotFoundException,
} from '@nestjs/common';
import { app, firestore } from 'firebase-admin';
import {
  VerifyWebhookQueryDto,
  WebhookContact,
  WebhookMessage,
  WebhookPayload,
  WebhookStatus,
} from './webhook.dto';
import { ProjectModel } from '../../../../commons/firestore-models/project/project.model.interface';
import dayjs from 'dayjs';
import { RawMessageModel } from '../../../../commons/firestore-models/message/message.model.interface';
import { RawChatModel } from '../../../../commons/firestore-models/chat/chat.model.interface';
import { ProjectModelFirestore } from '../../../../commons/firestore-models/project/project.model.firestore';

@Injectable()
export class WebhookService {
  private readonly logger = new Logger(WebhookService.name);
  private projectCollection: firestore.CollectionReference<ProjectModel>;
  constructor(
    @Inject('FIREBASE_ADMIN') private readonly firebaseAdmin: app.App,
  ) {
    this.projectCollection = this.firebaseAdmin
      .firestore()
      .collection('projects')
      .withConverter(ProjectModelFirestore.converter());
  }
  async verifyWebhook(projectId: string, query: VerifyWebhookQueryDto) {
    const docProject = this.projectCollection.doc(projectId);
    const getProject = await docProject.get();

    if (!getProject.exists) {
      throw new NotFoundException('Project not found');
    }

    const project = getProject.data()!;

    if (
      project.whatsappCredentials.webhookVerificationToken !==
      query['hub.verify_token']
    ) {
      throw new NotFoundException('Invalid verification token');
    }

    return query['hub.challenge'];
  }

  handleWebhook(projectId: string, body: WebhookPayload) {
    try {
      for (const entry of body.entry) {
        for (const change of entry.changes) {
          if (change.field !== 'messages') {
            continue;
          }

          change.value.messages?.forEach((message) => {
            const contacts: WebhookContact[] = change.value.contacts || [];
            this.handleIncomingMessage({
              projectId,
              message,
              contacts,
            })
              .then()
              .catch((reason) => {
                this.logger.error(
                  'Error handling incoming message:',
                  reason,
                  message,
                );
              });
          });

          change.value.statuses?.forEach((status) => {
            this.handleStatusUpdate({
              projectId,
              status,
            })
              .then()
              .catch((reason) => {
                this.logger.error(
                  'Error handling status update:',
                  reason,
                  status,
                );
              });
          });
        }
      }

      // Return standard acknowledgment
      return {
        message: 'EVENT_RECEIVED',
      };
    } catch (error) {
      this.logger.error('Error processing webhook:', error);
      throw new HttpException('Internal Server Error', 500);
    }
  }

  private readonly messageDetailKey = {
    text: 'text',
    image: 'image',
    audio: 'audio',
    video: 'video',
    document: 'document',
    sticker: 'sticker',
    location: 'location',
    contacts: 'contacts',
    reaction: 'reaction',
    interactive: 'interactive',
    button: 'button',
    order: 'order',
    system: 'system',
    unsupported: 'errors',
  } as const satisfies Record<WebhookMessage['type'], keyof WebhookMessage>;

  private async handleIncomingMessage(params: {
    projectId: string;
    message: WebhookMessage;
    contacts: WebhookContact[];
  }) {
    const { projectId, message, contacts } = params;

    const now = dayjs().toDate();

    const profileName = contacts[0]?.profile?.name;
    const phoneNumber = message.from;

    const chatsCollection = this.projectCollection
      .doc(projectId)
      .collection('chats');

    const chatDoc = chatsCollection.doc(phoneNumber);

    const getChatDoc = await chatDoc.get();

    const batch = this.firebaseAdmin.firestore().batch();

    if (!getChatDoc.exists) {
      const data: RawChatModel<Date> = {
        phoneNumber,
        name: profileName || phoneNumber,
        createdAt: now,
        updatedAt: now,
        lastMessage: null,
      };
      batch.set(chatDoc, data);
    } else {
      const data: Partial<RawChatModel<Date>> = {
        updatedAt: now,
        lastMessage: null,
      };
      batch.update(chatDoc, data);
    }

    const messagesCollection = chatDoc.collection('messages');
    const messageType = this.messageDetailKey[message.type];
    const messageDetailType = message[messageType];

    const messageBubble: RawMessageModel<Date> = {
      message: {
        id: message.id,
        from: message.from,
        timestamp: dayjs.unix(Number(message.timestamp)).toDate(),
        type: message.type,
        [messageType as WebhookMessage['type']]: {
          ...(messageDetailType as object),
        },
        context: message.context
          ? {
              forwarded: message.context.forwarded || false,
              frequently_forwarded:
                message.context.frequently_forwarded || false,
              from: message.context.from || null,
              id: message.context.id || null,
              referred_product: message.context.referred_product,
            }
          : null,
      },
      statuses: null,
      direction: 'in',
      createdAt: now,
    };
    batch.set(messagesCollection.doc(message.id), messageBubble);

    batch.update(chatDoc, {
      lastMessage: messageBubble,
    });

    await batch.commit();
  }

  private async handleStatusUpdate(params: {
    projectId: string;
    status: WebhookStatus;
  }) {
    const messageId = params.status.id;
    const status = params.status.status;
    const timestamp = dayjs.unix(Number(params.status.timestamp)).toDate();

    const chatsCollection = this.projectCollection
      .doc(params.projectId)
      .collection('chats');
    const chatDoc = chatsCollection.doc(params.status.recipient_id);

    const messagesCollection = chatDoc.collection('messages');
    const messageDoc = messagesCollection.doc(messageId);

    const getMessageDoc = await messageDoc.get();
    if (!getMessageDoc.exists) {
      return;
    }

    const batch = this.firebaseAdmin.firestore().batch();

    batch.update(chatDoc, {
      'lastMessage.statuses.latest': status,
      'lastMessage.statuses.timestamp': timestamp,
      [`lastMessage.statuses.details.${status}`]: timestamp,
    });

    batch.update(messageDoc, {
      'statuses.latest': status,
      'statuses.timestamp': timestamp,
      [`statuses.details.${status}`]: timestamp,
    });

    await batch.commit();
  }
}
