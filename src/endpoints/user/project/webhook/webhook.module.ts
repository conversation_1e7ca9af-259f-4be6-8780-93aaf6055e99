import {
  MiddlewareConsumer,
  Module,
  NestModule,
  RequestMethod,
} from '@nestjs/common';
import { WebhookService } from './webhook.service';
import { WebhookController } from './webhook.controller';
import { WebhookMiddleware } from './webhook.middleware';

@Module({
  controllers: [WebhookController],
  providers: [WebhookService],
})
export class WebhookModule implements NestModule {
  configure(consumer: MiddlewareConsumer) {
    consumer.apply(WebhookMiddleware).forRoutes({
      path: '/user/project/:projectId/webhook',
      method: RequestMethod.POST,
    });
  }
}
