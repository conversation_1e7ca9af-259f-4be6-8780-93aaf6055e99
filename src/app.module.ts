import { ZodSerializerInterceptor, ZodValidationPipe } from 'nestjs-zod';
import { APP_FILTER, APP_INTERCEPTOR, APP_PIPE } from '@nestjs/core';
import { Module } from '@nestjs/common';
import { AppController } from './app.controller';
import { AppService } from './app.service';
import { ConfigModule } from '@nestjs/config';
import { FirebaseModule } from './commons/modules/firebase/firebase.module';
import { EndpointModule } from './endpoints/endpoint.module';
import { FirebaseAdminException } from './commons/exception-filters/firebase-admin.exception';
import { ZodException } from './commons/exception-filters/zod.exception';

@Module({
  imports: [
    ConfigModule.forRoot({
      isGlobal: true,
      envFilePath: '.env',
    }),
    FirebaseModule.forRoot(),
    EndpointModule,
  ],
  controllers: [AppController],
  providers: [
    AppService,
    {
      provide: APP_PIPE,
      useClass: ZodValidationPipe,
    },
    {
      provide: APP_INTERCEPTOR,
      useClass: ZodSerializerInterceptor,
    },
    {
      provide: APP_FILTER,
      useClass: FirebaseAdminException,
    },
    {
      provide: APP_FILTER,
      useClass: ZodException,
    },
  ],
})
export class AppModule {}
