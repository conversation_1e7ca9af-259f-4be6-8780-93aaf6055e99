# Ideal Public Backend

Backend service berbasis [NestJS](https://nestjs.com) untuk mengelola alur onboarding klien Ideal, mulai dari registrasi akun hingga integrasi proyek dengan WhatsApp Business melalui Firebase. Aplikasi ini mengandalkan Firebase Authentication untuk manajemen identitas dan Cloud Firestore sebagai penyimpanan data proyek.

## Ringkasan Fitur
- Registrasi klien baru melalui Firebase Admin SDK dan penetapan custom claim `type: client`.
- Proteksi endpoint menggunakan guard yang memverifikasi Firebase ID Token.
- Manajemen proyek pengguna (lihat semua, detail per proyek, dan pembaruan metadata) langsung terhadap koleksi `projects` di Cloud Firestore.
- Endpoint webhook WhatsApp Business untuk verifikasi dan ingest pesan/status, lengkap dengan middleware pencatatan payload ke subkoleksi `meta_webhooks`.
- Validasi request menggunakan `nestjs-zod` dan filter exception terdedikasi untuk respons yang konsisten.

## Prasyarat
- Node.js 20.x (NestJS 11 mensyaratkan Node 18+, namun proyek ini diuji dengan Node 20).
- NPM 10.x (atau versinya yang tersedia pada Node 20).
- Akses ke Firebase project dengan service account yang memiliki hak Firebase Auth dan Cloud Firestore.

## Konfigurasi Lingkungan
1. Salin berkas `.env` atau buat baru, minimal berisi:
   ```env
   PORT=8000 # opsional, default 3000
   ```
2. Siapkan kredensial service account Firebase Admin SDK.
   - Secara default `FirebaseModule` mencari berkas pada `src/keys/ideal-lumatera-firebase-adminsdk-fbsvc-e5e40d43f4.json`.
   - **Disarankan** menyimpan berkas asli di luar repositori dan memberikan path kustom melalui `FirebaseModule.forRoot({ keyPath: '<path Anda>' })` sebelum deploy.
   - Jangan commit kredensial produksi ke git.

## Instalasi & Menjalankan
```bash
npm install
npm run start:dev       # pengembangan dengan hot-reload
npm run start           # mode development tanpa watch
npm run build           # kompilasi ke direktori dist
npm run start:prod      # jalan dari output build
```

## Pengujian & Kualitas Kode
```bash
npm test         # unit test
npm run test:e2e # e2e test (Supertest)
npm run test:cov # laporan coverage
npm run lint     # ESLint
npm run format   # Prettier
```
Pastikan seluruh suite hijau sebelum membuka PR.

## Struktur Direktori
```
src/
├── app.module.ts           # konfigurasi modul utama & provider global
├── commons/                # guard, decorator, exception filter, dan modul bersama
│   ├── decorators/current-user.decorator.ts
│   ├── exception-filters/  # penanganan error Firebase & Zod
│   ├── guards/auth.guard.ts
│   └── modules/firebase/   # inisialisasi Firebase Admin
├── endpoints/              # modul endpoint terkelompok
│   ├── register/           # registrasi pengguna baru
│   └── user/
│       ├── account/        # profil akun (JWT decode)
│       └── project/        # manajemen proyek & webhook WhatsApp
└── main.ts                 # bootstrap NestJS
```

## Arsitektur & Alur Utama
- **Autentikasi**: Guard `AuthGuard` memverifikasi header `Authorization: Bearer <ID_TOKEN>` menggunakan Firebase Admin. Decorator `@CurrentUser()` menyuntikkan token terverifikasi ke handler.
- **Validasi**: Setiap DTO dibuat dari skema `zod` dan dipakai bersamaan dengan `ZodValidationPipe` serta `ZodSerializerInterceptor` global untuk menjaga input/output tetap konsisten.
- **Exception Handling**: Kesalahan Firebase Auth diterjemahkan oleh `FirebaseAdminException`, sedangkan kesalahan validasi ditangani `ZodException` dengan struktur respons 422.
- **Firebase**: `FirebaseModule` membuat provider global `FIREBASE_ADMIN` sehingga service/guard lain dapat melakukan dependency injection.
- **Webhook Logging**: `WebhookMiddleware` menulis payload mentah ke subkoleksi `projects/{projectId}/meta_webhooks` lengkap dengan timestamp (`dayjs` dipakai untuk konversi UNIX timestamp WhatsApp).

## Referensi Endpoint
| Method | Path                              | Deskripsi | Proteksi |
|--------|-----------------------------------|-----------|----------|
| POST   | `/register`                       | Membuat user Firebase baru dan mengembalikan detailnya. | Publik |
| GET    | `/user/account`                   | Mengembalikan token terverifikasi pengguna saat ini. | Firebase ID Token |
| GET    | `/user/project`                   | Mengambil seluruh proyek milik pengguna (`ownerUserUid`). | Firebase ID Token |
| GET    | `/user/project/:id`               | Mendapatkan detail proyek tertentu; 404 jika tidak ditemukan. | Firebase ID Token |
| PUT    | `/user/project/:id`               | Memperbarui `projectName`/`description` dan `updatedAt`. | Firebase ID Token |
| GET    | `/user/project/webhook/:projectId`| Endpoint verifikasi WhatsApp webhook (`hub.challenge`). | Token verifikasi di query |
| POST   | `/user/project/webhook/:projectId`| Menangani event webhook dan mencatat payload. | Signature WhatsApp (opsional) |

Semua endpoint terproteksi membutuhkan header `Authorization: Bearer <firebase-id-token>` kecuali endpoint publik yang disebutkan.

## Tips Pengembangan
- Jalankan `npm run lint` dan `npm run format` sebelum commit.
- Gunakan emulator Firebase saat pengembangan lokal untuk menghindari biaya/kuota produksi.
- Saat mengubah path kredensial Firebase, pertimbangkan penggunaan variabel lingkungan agar mudah diatur antar environment.

## Lisensi
Proyek ini bersifat private (`UNLICENSED`). Hubungi tim Ideal jika memerlukan akses atau penggunaan ulang.
